# FileScopeMCP 使用说明

## 📋 工具概述

FileScopeMCP 是一个强大的代码库分析工具，专门为AI助手设计，用于理解和分析代码库结构。它能够：

- **依赖关系分析**：基于文件间的依赖关系识别重要文件
- **重要性评分**：为每个文件生成重要性分数（0-10）
- **可视化图表**：生成Mermaid架构图和依赖关系图
- **多语言支持**：自动解析Python、C、C++、Rust、Zig、Lua等流行编程语言
- **实时监控**：监控文件变化并自动更新分析结果

## 🔧 安装与配置

### 1. 安装方式

```bash
# 克隆仓库
git clone https://github.com/admica/FileScopeMCP.git
cd FileScopeMCP

# 安装依赖
npm install

# 构建项目
npm run build
```

### 2. MCP配置

**Windows配置** (`mcp.json`):
```json
{
  "mcpServers": {
    "FileScopeMCP": {
      "command": "node",
      "args": ["path/to/FileScopeMCP/dist/mcp-server.js"]
    }
  }
}
```

**Linux/Mac配置**:
```json
{
  "mcpServers": {
    "FileScopeMCP": {
      "command": "node",
      "args": ["/path/to/FileScopeMCP/dist/mcp-server.js"]
    }
  }
}
```

### 3. 配置文件 (config.json)

```json
{
  "fileWatching": {
    "enabled": true,
    "watchForNewFiles": true,
    "watchForChanged": true,
    "watchForDeleted": true,
    "autoRebuildTree": true,
    "debounceMs": 500,
    "maxWatchedDirectories": 100,
    "ignoreDotFiles": true
  },
  "analysis": {
    "maxDepth": 10,
    "minImportance": 0,
    "excludePatterns": [
      "node_modules/**",
      ".git/**",
      "dist/**",
      "build/**",
      "*.log"
    ]
  },
  "diagram": {
    "defaultStyle": "hybrid",
    "maxNodes": 100,
    "showDependencies": true,
    "packageGrouping": true
  }
}
```

## 🛠️ 核心功能与API

### 1. 项目初始化

#### `set_project_path_FileScopeMCP`
设置要分析的项目路径
```typescript
参数: { path: string }
返回: 成功/失败状态
```

#### `create_file_tree_FileScopeMCP`
创建文件树配置
```typescript
参数: { 
  filename: string,      // JSON文件名
  baseDirectory: string  // 基础目录路径
}
返回: 文件树创建状态
```

#### `select_file_tree_FileScopeMCP`
选择要使用的文件树
```typescript
参数: { filename: string }
返回: 选择状态
```

### 2. 文件分析

#### `list_files_FileScopeMCP`
列出所有文件及其重要性排名
```typescript
参数: 无
返回: [
  {
    filepath: string,
    importance: number,    // 0-10
    summary: string,
    dependencies: string[]
  }
]
```

#### `find_important_files_FileScopeMCP`
查找最重要的文件
```typescript
参数: { 
  limit?: number,        // 返回文件数量，默认10
  minImportance?: number // 最小重要性分数，默认0
}
返回: 重要文件列表
```

#### `get_file_importance_FileScopeMCP`
获取特定文件的重要性评分
```typescript
参数: { filepath: string }
返回: { 
  filepath: string,
  importance: number,
  reasons: string[]      // 重要性原因
}
```

#### `set_file_importance_FileScopeMCP`
手动设置文件重要性
```typescript
参数: { 
  filepath: string,
  importance: number     // 0-10
}
返回: 设置状态
```

### 3. 文件内容管理

#### `read_file_content_FileScopeMCP`
读取文件内容
```typescript
参数: { filepath: string }
返回: { 
  content: string,
  encoding: string,
  size: number
}
```

#### `get_file_summary_FileScopeMCP`
获取文件摘要
```typescript
参数: { filepath: string }
返回: { 
  filepath: string,
  summary: string,
  lastUpdated: string
}
```

#### `set_file_summary_FileScopeMCP`
设置文件摘要
```typescript
参数: { 
  filepath: string,
  summary: string
}
返回: 设置状态
```

### 4. 实时监控

#### `toggle_file_watching_FileScopeMCP`
启用/禁用文件监控
```typescript
参数: 无
返回: { 
  enabled: boolean,
  watchedDirectories: number,
  watchedFiles: number
}
```

#### `get_file_watching_status_FileScopeMCP`
获取文件监控状态
```typescript
参数: 无
返回: {
  enabled: boolean,
  config: FileWatchingConfig,
  stats: WatchingStats
}
```

#### `update_file_watching_config_FileScopeMCP`
更新文件监控配置
```typescript
参数: { 
  config: {
    enabled?: boolean,
    watchForNewFiles?: boolean,
    watchForChanged?: boolean,
    watchForDeleted?: boolean,
    autoRebuildTree?: boolean,
    debounceMs?: number,
    maxWatchedDirectories?: number,
    ignoreDotFiles?: boolean
  }
}
返回: 更新状态
```

### 5. 可视化生成

#### `generate_diagram_FileScopeMCP`
生成Mermaid架构图
```typescript
参数: {
  style: "default" | "dependency" | "directory" | "hybrid" | "package-deps",
  outputFormat?: "mmd" | "html",           // 默认mmd
  outputPath?: string,                     // 输出文件路径
  maxDepth?: number,                       // 最大深度1-10
  minImportance?: number,                  // 最小重要性0-10
  showDependencies?: boolean,              // 显示依赖关系
  showPackageDeps?: boolean,               // 显示包依赖
  packageGrouping?: boolean,               // 包分组
  includeOnlyPackages?: string[],          // 仅包含指定包
  excludePackages?: string[],              // 排除指定包
  autoGroupThreshold?: number,             // 自动分组阈值，默认8
  layout?: {
    direction: "TB" | "BT" | "LR" | "RL",  // 图表方向
    nodeSpacing: number,                   // 节点间距10-100
    rankSpacing: number                    // 层级间距10-100
  }
}
返回: {
  diagramContent: string,
  outputPath?: string,
  nodeCount: number,
  edgeCount: number
}
```

### 6. 数据管理

#### `recalculate_importance_FileScopeMCP`
重新计算所有文件的重要性
```typescript
参数: 无
返回: {
  processedFiles: number,
  updatedFiles: number,
  averageImportance: number
}
```

#### `exclude_and_remove_FileScopeMCP`
排除并移除文件或模式
```typescript
参数: { filepath: string }  // 文件路径或glob模式
返回: {
  excluded: string[],
  removed: string[]
}
```

## 📊 使用示例

### 基础工作流程

```typescript
// 1. 设置项目路径
await set_project_path_FileScopeMCP({ path: "/path/to/project" });

// 2. 创建文件树
await create_file_tree_FileScopeMCP({ 
  filename: "project_tree.json", 
  baseDirectory: "/path/to/project" 
});

// 3. 启用文件监控
await toggle_file_watching_FileScopeMCP();

// 4. 查找重要文件
const importantFiles = await find_important_files_FileScopeMCP({ 
  limit: 20, 
  minImportance: 7 
});

// 5. 生成架构图
await generate_diagram_FileScopeMCP({
  style: "hybrid",
  outputFormat: "html",
  outputPath: "docs/architecture.html",
  showDependencies: true,
  minImportance: 5
});
```

### 文档同步工作流程

```typescript
// 代码修改后的文档更新流程
async function updateDocumentationAfterCodeChange() {
  // 1. 检查监控状态
  const status = await get_file_watching_status_FileScopeMCP();
  
  // 2. 重新计算重要性
  await recalculate_importance_FileScopeMCP();
  
  // 3. 获取变更影响的重要文件
  const importantFiles = await find_important_files_FileScopeMCP({ 
    limit: 10, 
    minImportance: 8 
  });
  
  // 4. 更新文件摘要
  for (const file of importantFiles) {
    await set_file_summary_FileScopeMCP({
      filepath: file.filepath,
      summary: `Updated: ${new Date().toISOString()}`
    });
  }
  
  // 5. 生成最新架构图
  await generate_diagram_FileScopeMCP({
    style: "dependency",
    outputFormat: "html",
    outputPath: "docs/updated_architecture.html"
  });
}
```

## 🎨 图表样式说明

### 样式类型
- **default**: 基础文件结构图
- **dependency**: 依赖关系图，突出显示文件间依赖
- **directory**: 目录结构图，按文件夹组织
- **hybrid**: 混合模式，结合目录结构和依赖关系
- **package-deps**: 包依赖图，显示包级别的依赖关系

### 输出格式
- **mmd**: Mermaid源码格式，可在支持Mermaid的编辑器中查看
- **html**: 完整的HTML文件，包含交互式图表

## ⚙️ 高级配置

### 文件监控优化
```json
{
  "fileWatching": {
    "debounceMs": 1000,              // 增加防抖时间减少频繁更新
    "maxWatchedDirectories": 50,     // 限制监控目录数量提高性能
    "ignoreDotFiles": true,          // 忽略隐藏文件
    "autoRebuildTree": false         // 禁用自动重建以手动控制
  }
}
```

### 分析性能调优
```json
{
  "analysis": {
    "maxDepth": 5,                   // 限制分析深度
    "excludePatterns": [
      "**/*.test.*",
      "**/*.spec.*",
      "**/coverage/**",
      "**/docs/**"
    ]
  }
}
```

## 🔍 故障排除

### 常见问题

1. **文件监控不工作**
   - 检查文件系统权限
   - 确认监控目录数量未超限
   - 验证路径格式正确

2. **重要性评分异常**
   - 运行 `recalculate_importance_FileScopeMCP`
   - 检查文件依赖关系是否正确解析
   - 验证排除模式是否过于宽泛

3. **图表生成失败**
   - 检查输出路径权限
   - 确认节点数量未超过限制
   - 验证Mermaid语法正确性

### 性能优化建议

- 合理设置 `maxDepth` 和 `minImportance` 参数
- 使用排除模式忽略不必要的文件
- 定期清理过时的文件树配置
- 根据项目大小调整监控参数

## 📁 文件树管理

### 保存的文件树列表

#### `list_saved_trees_FileScopeMCP`
列出所有保存的文件树配置
```typescript
参数: 无
返回: [
  {
    filename: string,
    created: string,
    size: number,
    baseDirectory: string
  }
]
```

#### `delete_file_tree_FileScopeMCP`
删除指定的文件树配置
```typescript
参数: { filename: string }
返回: { deleted: boolean, filename: string }
```

### 调试功能

#### `debug_list_all_files_FileScopeMCP`
列出当前文件树中的所有文件路径
```typescript
参数: 无
返回: {
  totalFiles: number,
  files: string[],
  directories: string[]
}
```

## 🔄 集成工作流程

### 与Augment Agent集成

FileScopeMCP专为Augment Agent优化，提供以下集成模式：

#### 1. 任务开始前的基线建立
```typescript
// 标准初始化流程
async function establishBaseline(projectPath: string) {
  // 设置项目路径
  await set_project_path_FileScopeMCP({ path: projectPath });

  // 创建或选择文件树
  const treeFilename = `${path.basename(projectPath)}_tree.json`;
  await create_file_tree_FileScopeMCP({
    filename: treeFilename,
    baseDirectory: projectPath
  });

  // 启用实时监控
  await toggle_file_watching_FileScopeMCP();

  // 生成基线架构图
  await generate_diagram_FileScopeMCP({
    style: "hybrid",
    outputPath: "baseline_architecture.html",
    outputFormat: "html"
  });

  return { treeFilename, baselineGenerated: true };
}
```

#### 2. 代码修改后的实时更新
```typescript
// 代码变更后的自动更新流程
async function handleCodeChange() {
  // 检查文件监控状态
  const watchStatus = await get_file_watching_status_FileScopeMCP();

  if (!watchStatus.enabled) {
    await toggle_file_watching_FileScopeMCP();
  }

  // 重新计算重要性评分
  const recalcResult = await recalculate_importance_FileScopeMCP();

  // 识别受影响的重要文件
  const importantFiles = await find_important_files_FileScopeMCP({
    limit: 15,
    minImportance: 6
  });

  // 生成变更影响图
  await generate_diagram_FileScopeMCP({
    style: "dependency",
    outputPath: "change_impact.html",
    showDependencies: true,
    minImportance: 5
  });

  return {
    affectedFiles: importantFiles.length,
    recalculatedFiles: recalcResult.processedFiles
  };
}
```

#### 3. 文档同步检查流程
```typescript
// 文档同步状态检查
async function checkDocumentationSync() {
  // 获取所有重要文件
  const importantFiles = await find_important_files_FileScopeMCP({
    minImportance: 7
  });

  const syncStatus = [];

  for (const file of importantFiles) {
    // 检查文件摘要
    const summary = await get_file_summary_FileScopeMCP({
      filepath: file.filepath
    });

    // 读取文件内容获取最后修改时间
    const content = await read_file_content_FileScopeMCP({
      filepath: file.filepath
    });

    syncStatus.push({
      filepath: file.filepath,
      importance: file.importance,
      hasSummary: !!summary.summary,
      needsUpdate: !summary.lastUpdated ||
                   new Date(summary.lastUpdated) < new Date(content.lastModified)
    });
  }

  return syncStatus;
}
```

### 任务完成前的验证流程
```typescript
// 任务完成前的最终验证
async function finalValidation() {
  // 生成最终架构对比图
  await generate_diagram_FileScopeMCP({
    style: "hybrid",
    outputPath: "final_architecture.html",
    outputFormat: "html",
    showDependencies: true
  });

  // 获取项目概览
  const allFiles = await debug_list_all_files_FileScopeMCP();
  const importantFiles = await find_important_files_FileScopeMCP({
    minImportance: 5
  });

  // 检查文档覆盖率
  const documentedFiles = [];
  for (const file of importantFiles) {
    const summary = await get_file_summary_FileScopeMCP({
      filepath: file.filepath
    });
    if (summary.summary) {
      documentedFiles.push(file.filepath);
    }
  }

  return {
    totalFiles: allFiles.totalFiles,
    importantFiles: importantFiles.length,
    documentedFiles: documentedFiles.length,
    documentationCoverage: (documentedFiles.length / importantFiles.length) * 100
  };
}
```

## 🎯 最佳实践

### 1. 项目初始化最佳实践
- 总是在项目根目录运行初始化
- 使用描述性的文件树名称（如：`projectname_v1.0_tree.json`）
- 在开始开发前建立基线架构图
- 配置合适的排除模式避免分析无关文件

### 2. 实时监控最佳实践
- 根据项目大小调整 `debounceMs` 参数
- 大型项目建议设置较小的 `maxWatchedDirectories`
- 定期检查监控状态确保正常运行
- 在密集开发期间可临时禁用自动重建

### 3. 重要性评分最佳实践
- 定期运行 `recalculate_importance_FileScopeMCP`
- 手动调整核心文件的重要性评分
- 使用 `minImportance` 过滤器聚焦关键文件
- 结合文件摘要提供上下文信息

### 4. 可视化生成最佳实践
- 根据用途选择合适的图表样式
- 大型项目使用 `minImportance` 限制节点数量
- 定期生成HTML格式图表便于分享
- 保存不同阶段的架构图用于对比

### 5. 文档同步最佳实践
- 代码修改后立即更新相关文件摘要
- 使用时间戳跟踪文档更新状态
- 建立文档覆盖率目标（建议>90%）
- 定期审查和清理过时的文档信息

## 🔧 配置模板

### 小型项目配置
```json
{
  "fileWatching": {
    "enabled": true,
    "debounceMs": 300,
    "maxWatchedDirectories": 20,
    "autoRebuildTree": true
  },
  "analysis": {
    "maxDepth": 8,
    "minImportance": 0,
    "excludePatterns": ["node_modules/**", ".git/**"]
  },
  "diagram": {
    "defaultStyle": "hybrid",
    "maxNodes": 50
  }
}
```

### 大型项目配置
```json
{
  "fileWatching": {
    "enabled": true,
    "debounceMs": 1000,
    "maxWatchedDirectories": 100,
    "autoRebuildTree": false
  },
  "analysis": {
    "maxDepth": 6,
    "minImportance": 3,
    "excludePatterns": [
      "node_modules/**", ".git/**", "dist/**", "build/**",
      "**/*.test.*", "**/*.spec.*", "**/coverage/**"
    ]
  },
  "diagram": {
    "defaultStyle": "package-deps",
    "maxNodes": 200,
    "packageGrouping": true
  }
}
```

---

**版本**: 基于 admica/FileScopeMCP 主分支
**更新日期**: 2025-08-03
**许可证**: GPL-3.0
**GitHub**: https://github.com/admica/FileScopeMCP
