# FileScopeMCP 功能总结

## 🎯 核心功能概览

FileScopeMCP 是一个专为AI助手设计的代码库分析工具，提供以下核心能力：

### 1. 项目结构分析
- **文件依赖关系分析**：自动解析文件间的导入/引用关系
- **重要性评分系统**：基于依赖关系为每个文件生成0-10的重要性分数
- **多语言支持**：支持Python、C、C++、Rust、Zig、Lua等主流编程语言
- **智能排除机制**：自动忽略不相关文件（如node_modules、.git等）

### 2. 实时监控能力
- **文件变化监控**：实时监控文件的新增、修改、删除
- **自动重新分析**：文件变化时自动更新依赖关系和重要性评分
- **可配置监控参数**：支持防抖、目录限制、文件类型过滤等配置
- **性能优化**：智能限制监控范围避免性能问题

### 3. 可视化生成
- **Mermaid图表生成**：支持多种样式的架构图和依赖图
- **交互式HTML输出**：生成可交互的网页版架构图
- **多种图表样式**：dependency、directory、hybrid、package-deps等
- **自定义布局**：支持方向、间距、分组等布局参数

## 🔧 API功能分类

### 项目管理类
| 功能 | API | 用途 |
|------|-----|------|
| 设置项目路径 | `set_project_path_FileScopeMCP` | 指定要分析的项目根目录 |
| 创建文件树 | `create_file_tree_FileScopeMCP` | 建立项目文件结构配置 |
| 选择文件树 | `select_file_tree_FileScopeMCP` | 切换到指定的文件树配置 |
| 列出文件树 | `list_saved_trees_FileScopeMCP` | 查看所有保存的文件树 |
| 删除文件树 | `delete_file_tree_FileScopeMCP` | 删除不需要的文件树配置 |

### 文件分析类
| 功能 | API | 用途 |
|------|-----|------|
| 列出所有文件 | `list_files_FileScopeMCP` | 获取文件列表及重要性排名 |
| 查找重要文件 | `find_important_files_FileScopeMCP` | 筛选高重要性文件 |
| 获取文件重要性 | `get_file_importance_FileScopeMCP` | 查看特定文件的重要性评分 |
| 设置文件重要性 | `set_file_importance_FileScopeMCP` | 手动调整文件重要性 |
| 重新计算重要性 | `recalculate_importance_FileScopeMCP` | 刷新所有文件的重要性评分 |

### 内容管理类
| 功能 | API | 用途 |
|------|-----|------|
| 读取文件内容 | `read_file_content_FileScopeMCP` | 获取文件的完整内容 |
| 获取文件摘要 | `get_file_summary_FileScopeMCP` | 查看文件的描述摘要 |
| 设置文件摘要 | `set_file_summary_FileScopeMCP` | 为文件添加或更新摘要 |
| 排除文件 | `exclude_and_remove_FileScopeMCP` | 从分析中排除特定文件或模式 |

### 监控管理类
| 功能 | API | 用途 |
|------|-----|------|
| 切换文件监控 | `toggle_file_watching_FileScopeMCP` | 启用或禁用实时文件监控 |
| 获取监控状态 | `get_file_watching_status_FileScopeMCP` | 查看当前监控配置和状态 |
| 更新监控配置 | `update_file_watching_config_FileScopeMCP` | 修改监控参数和行为 |

### 可视化生成类
| 功能 | API | 用途 |
|------|-----|------|
| 生成架构图 | `generate_diagram_FileScopeMCP` | 创建Mermaid格式的项目架构图 |

### 调试工具类
| 功能 | API | 用途 |
|------|-----|------|
| 列出所有文件路径 | `debug_list_all_files_FileScopeMCP` | 调试用：查看文件树中的所有路径 |

## 📊 重要性评分机制

### 评分算法
FileScopeMCP使用多因素算法计算文件重要性：

1. **依赖关系权重**（40%）
   - 被其他文件导入的次数
   - 导入其他文件的数量
   - 依赖链的深度和广度

2. **文件类型权重**（25%）
   - 配置文件：高权重
   - 入口文件：高权重
   - 测试文件：低权重
   - 文档文件：中等权重

3. **代码复杂度权重**（20%）
   - 文件大小
   - 函数/类的数量
   - 代码行数

4. **项目结构权重**（15%）
   - 文件在目录结构中的位置
   - 是否为核心模块
   - 是否为公共接口

### 评分范围
- **9-10分**：核心架构文件，项目关键组件
- **7-8分**：重要业务逻辑文件
- **5-6分**：一般功能模块文件
- **3-4分**：辅助工具和配置文件
- **1-2分**：测试文件和文档文件
- **0分**：临时文件和无关文件

## 🎨 图表样式详解

### 1. default（默认样式）
- **用途**：基础文件结构展示
- **特点**：简洁清晰，适合初步了解项目结构
- **推荐场景**：项目概览、新人介绍

### 2. dependency（依赖关系图）
- **用途**：突出显示文件间的依赖关系
- **特点**：箭头表示依赖方向，颜色区分依赖强度
- **推荐场景**：代码重构、影响分析

### 3. directory（目录结构图）
- **用途**：按文件夹层次组织显示
- **特点**：树状结构，清晰的层级关系
- **推荐场景**：项目导航、文档编写

### 4. hybrid（混合模式）
- **用途**：结合目录结构和依赖关系
- **特点**：最全面的信息展示
- **推荐场景**：架构分析、全面理解

### 5. package-deps（包依赖图）
- **用途**：显示包级别的依赖关系
- **特点**：抽象层次更高，关注模块间关系
- **推荐场景**：架构设计、模块划分

## 🔄 典型使用场景

### 场景1：新项目分析
```
1. set_project_path_FileScopeMCP → 设置项目路径
2. create_file_tree_FileScopeMCP → 创建文件树
3. toggle_file_watching_FileScopeMCP → 启用监控
4. find_important_files_FileScopeMCP → 识别核心文件
5. generate_diagram_FileScopeMCP(style="hybrid") → 生成全景图
```

### 场景2：代码重构准备
```
1. find_important_files_FileScopeMCP(minImportance=8) → 找到核心文件
2. generate_diagram_FileScopeMCP(style="dependency") → 分析依赖关系
3. get_file_importance_FileScopeMCP → 评估重构影响
4. set_file_summary_FileScopeMCP → 记录重构计划
```

### 场景3：文档同步检查
```
1. list_files_FileScopeMCP → 获取所有文件
2. get_file_summary_FileScopeMCP → 检查文档状态
3. recalculate_importance_FileScopeMCP → 更新重要性
4. generate_diagram_FileScopeMCP → 生成最新架构图
```

### 场景4：代码审查准备
```
1. find_important_files_FileScopeMCP(limit=20) → 重点关注文件
2. read_file_content_FileScopeMCP → 读取关键文件内容
3. generate_diagram_FileScopeMCP(style="package-deps") → 模块关系图
4. set_file_summary_FileScopeMCP → 添加审查注释
```

## ⚡ 性能优化建议

### 大型项目优化
- 设置合理的 `maxDepth` 限制（建议6-8）
- 使用 `minImportance` 过滤低重要性文件
- 配置详细的 `excludePatterns` 排除无关文件
- 适当增加 `debounceMs` 减少频繁更新

### 监控性能优化
- 限制 `maxWatchedDirectories` 数量
- 启用 `ignoreDotFiles` 忽略隐藏文件
- 在密集开发时禁用 `autoRebuildTree`
- 定期清理不需要的文件树配置

### 图表生成优化
- 大型项目使用 `minImportance` 限制节点数
- 选择合适的图表样式避免过度复杂
- 使用 `packageGrouping` 简化包级别视图
- 设置合理的 `maxNodes` 限制

---

**工具特色**：智能分析 + 实时监控 + 可视化展示  
**适用场景**：代码理解、架构分析、文档同步、重构准备  
**集成优势**：专为AI助手优化，无缝集成开发工作流
